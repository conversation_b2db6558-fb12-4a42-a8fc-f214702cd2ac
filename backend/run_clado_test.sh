#!/bin/bash

# CladoTool Test Runner Script
# This script sets up the environment and runs the CladoTool functionality tests

echo "🧪 CladoTool Test Runner"
echo "======================="

# Check if we're in the backend directory
if [ ! -f "agent/tools/clado_tool.py" ]; then
    echo "❌ Error: Please run this script from the backend directory"
    echo "   Current directory: $(pwd)"
    echo "   Expected files: agent/tools/clado_tool.py"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found in backend directory"
    echo "   Please create a .env file with CLADO_API_KEY"
    exit 1
fi

# Load environment variables
echo "🔧 Loading environment variables from .env..."
set -a
source .env
set +a

# Check if CLADO_API_KEY is set
if [ -z "$CLADO_API_KEY" ]; then
    echo "❌ Error: CLADO_API_KEY not found in .env file"
    echo "   Please add: CLADO_API_KEY=your_api_key_here"
    exit 1
fi

echo "✅ CLADO_API_KEY found: ${CLADO_API_KEY:0:10}..."

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: python3 not found"
    echo "   Please install Python 3"
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check if required Python packages are available
echo "🔍 Checking Python dependencies..."

python3 -c "
import sys
import os
sys.path.insert(0, '.')

try:
    import httpx
    print('✅ httpx available')
except ImportError:
    print('❌ httpx not available - install with: pip install httpx')
    sys.exit(1)

try:
    import asyncio
    print('✅ asyncio available')
except ImportError:
    print('❌ asyncio not available')
    sys.exit(1)

try:
    from utils.config import config
    print('✅ utils.config available')
except ImportError as e:
    print(f'❌ utils.config not available: {e}')
    sys.exit(1)

try:
    from agentpress.tool import Tool, ToolResult
    print('✅ agentpress.tool available')
except ImportError as e:
    print(f'❌ agentpress.tool not available: {e}')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ Dependency check failed"
    exit 1
fi

echo ""
echo "🚀 All checks passed! Running CladoTool tests..."
echo "================================================"
echo ""

# Run the test
python3 test_clado_tool_functionality.py

# Capture exit code
exit_code=$?

echo ""
echo "================================================"
if [ $exit_code -eq 0 ]; then
    echo "🎉 Tests completed successfully!"
    echo ""
    echo "Next steps to debug agent tool exposure:"
    echo "1. Check agent configuration in database:"
    echo "   SELECT agent_id, name, agentpress_tools FROM agents WHERE agentpress_tools::text LIKE '%clado_tool%';"
    echo ""
    echo "2. Check agent runtime logs for tool registration:"
    echo "   Look for 'Using custom tool configuration from agent' in logs"
    echo ""
    echo "3. Test with a specific agent that has clado_tool enabled"
else
    echo "❌ Tests failed with exit code: $exit_code"
    echo ""
    echo "Common issues:"
    echo "1. Invalid or expired CLADO_API_KEY"
    echo "2. Network connectivity issues"
    echo "3. API rate limiting or insufficient credits"
    echo "4. Missing Python dependencies"
fi

exit $exit_code
