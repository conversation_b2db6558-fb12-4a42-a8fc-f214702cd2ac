#!/usr/bin/env python3
"""
Unit tests for CladoTool functionality.

This test file verifies that the CladoTool can be imported, initialized,
and that all its methods work correctly with real API calls.

Run with: python test_clado_tool_functionality.py
"""

import asyncio
import os
import sys
import json
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import required modules
from agent.tools.clado_tool import CladoTool
from agentpress.tool import ToolResult
from utils.logger import logger


class CladoToolTester:
    """Test class for CladoTool functionality."""

    def __init__(self):
        """Initialize the tester."""
        self.tool = None
        self.test_results = {}

    async def setup(self):
        """Set up the test environment."""
        print("🔧 Setting up CladoTool test environment...")

        try:
            # Initialize the CladoTool
            self.tool = CladoTool()
            print("✅ CladoTool initialized successfully")
            print(f"   API Key present: {'Yes' if self.tool.api_key else 'No'}")
            print(f"   Base URL: {self.tool.base_url}")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize CladoTool: {str(e)}")
            return False

    def print_result(self, test_name: str, result: ToolResult):
        """Print test result in a formatted way."""
        print(f"\n📊 {test_name} Results:")
        print(f"   Success: {result.success}")

        if result.success:
            # Try to parse and pretty print the output
            try:
                if isinstance(result.output, str):
                    data = json.loads(result.output)
                    print(f"   Data type: {type(data).__name__}")
                    if isinstance(data, dict):
                        print(f"   Keys: {list(data.keys())}")
                        if "results" in data:
                            print(
                                f"   Results count: {len(data['results']) if isinstance(data['results'], list) else 'N/A'}"
                            )
                    elif isinstance(data, list):
                        print(f"   Items count: {len(data)}")
                else:
                    print(f"   Output type: {type(result.output).__name__}")
                    print(f"   Output: {str(result.output)[:200]}...")
            except json.JSONDecodeError:
                print(f"   Output (raw): {str(result.output)[:200]}...")
        else:
            print(f"   Error: {result.output}")

    async def test_search_linkedin_users(self):
        """Test the search_linkedin_users function."""
        print("\n🔍 Testing search_linkedin_users...")

        try:
            result = await self.tool.search_linkedin_users(
                query="software engineers at tech companies",
                limit=5,
                acceptance_threshold=75,
            )
            self.test_results["search_linkedin_users"] = result.success
            self.print_result("Search LinkedIn Users", result)
            return result.success
        except Exception as e:
            print(f"❌ search_linkedin_users failed: {str(e)}")
            self.test_results["search_linkedin_users"] = False
            return False

    async def test_search_linkedin_companies(self):
        """Test the search_linkedin_companies function."""
        print("\n🏢 Testing search_linkedin_companies...")

        try:
            result = await self.tool.search_linkedin_companies(
                query="AI startups in healthcare", limit=5, acceptance_threshold=75
            )
            self.test_results["search_linkedin_companies"] = result.success
            self.print_result("Search LinkedIn Companies", result)
            return result.success
        except Exception as e:
            print(f"❌ search_linkedin_companies failed: {str(e)}")
            self.test_results["search_linkedin_companies"] = False
            return False

    async def test_enrich_linkedin_profile(self):
        """Test the enrich_linkedin_profile function."""
        print("\n👤 Testing enrich_linkedin_profile...")

        try:
            # Use a well-known LinkedIn profile URL for testing
            result = await self.tool.enrich_linkedin_profile(
                linkedin_url="https://www.linkedin.com/in/satyanadella/"
            )
            self.test_results["enrich_linkedin_profile"] = result.success
            self.print_result("Enrich LinkedIn Profile", result)
            return result.success
        except Exception as e:
            print(f"❌ enrich_linkedin_profile failed: {str(e)}")
            self.test_results["enrich_linkedin_profile"] = False
            return False

    async def test_get_linkedin_contacts(self):
        """Test the get_linkedin_contacts function."""
        print("\n📧 Testing get_linkedin_contacts...")

        try:
            # Test with a LinkedIn URL
            result = await self.tool.get_linkedin_contacts(
                linkedin_url="https://www.linkedin.com/in/satyanadella/"
            )
            self.test_results["get_linkedin_contacts"] = result.success
            self.print_result("Get LinkedIn Contacts", result)
            return result.success
        except Exception as e:
            print(f"❌ get_linkedin_contacts failed: {str(e)}")
            self.test_results["get_linkedin_contacts"] = False
            return False

    async def test_scrape_linkedin_profile(self):
        """Test the scrape_linkedin_profile function."""
        print("\n🕷️ Testing scrape_linkedin_profile...")

        try:
            result = await self.tool.scrape_linkedin_profile(
                linkedin_url="https://www.linkedin.com/in/satyanadella/"
            )
            self.test_results["scrape_linkedin_profile"] = result.success
            self.print_result("Scrape LinkedIn Profile", result)
            return result.success
        except Exception as e:
            print(f"❌ scrape_linkedin_profile failed: {str(e)}")
            self.test_results["scrape_linkedin_profile"] = False
            return False

    async def test_start_deep_research(self):
        """Test the start_deep_research function."""
        print("\n🔬 Testing start_deep_research...")

        try:
            result = await self.tool.start_deep_research(
                query="AI engineers at tech companies",
                limit=10,
                enrich_emails=True,
                acceptance_threshold=80,
            )
            self.test_results["start_deep_research"] = result.success
            self.print_result("Start Deep Research", result)

            # If successful, try to get the job status
            if result.success:
                try:
                    output_data = json.loads(result.output)
                    if "job_id" in output_data:
                        job_id = output_data["job_id"]
                        print(f"   Job ID obtained: {job_id}")

                        # Test get_deep_research_status with the job ID
                        await asyncio.sleep(2)  # Wait a bit before checking status
                        await self.test_get_deep_research_status(job_id)
                except json.JSONDecodeError:
                    print("   Could not parse job_id from response")

            return result.success
        except Exception as e:
            print(f"❌ start_deep_research failed: {str(e)}")
            self.test_results["start_deep_research"] = False
            return False

    async def test_get_deep_research_status(self, job_id: str = None):
        """Test the get_deep_research_status function."""
        print(f"\n📈 Testing get_deep_research_status...")

        if not job_id:
            # Use a dummy job ID for testing
            job_id = "test-job-id-12345"

        try:
            result = await self.tool.get_deep_research_status(job_id=job_id)
            self.test_results["get_deep_research_status"] = result.success
            self.print_result("Get Deep Research Status", result)
            return result.success
        except Exception as e:
            print(f"❌ get_deep_research_status failed: {str(e)}")
            self.test_results["get_deep_research_status"] = False
            return False

    async def run_all_tests(self):
        """Run all tests in sequence."""
        print("🚀 Starting CladoTool functionality tests...\n")

        # Setup
        if not await self.setup():
            print("❌ Setup failed. Cannot proceed with tests.")
            return False

        # Run individual tests
        tests = [
            self.test_search_linkedin_users,
            self.test_search_linkedin_companies,
            self.test_enrich_linkedin_profile,
            self.test_get_linkedin_contacts,
            self.test_scrape_linkedin_profile,
            self.test_start_deep_research,
            # Note: get_deep_research_status is called from start_deep_research
        ]

        print("\n" + "=" * 60)
        print("🧪 Running individual function tests...")
        print("=" * 60)

        for test in tests:
            try:
                await test()
                await asyncio.sleep(1)  # Small delay between tests
            except Exception as e:
                print(f"❌ Test {test.__name__} crashed: {str(e)}")

        # Print summary
        self.print_summary()

        return any(self.test_results.values())

    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for success in self.test_results.values() if success)
        failed_tests = total_tests - passed_tests

        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")

        print("\nDetailed results:")
        for test_name, success in self.test_results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"  {test_name}: {status}")

        if passed_tests > 0:
            print(f"\n🎉 {passed_tests} test(s) passed! CladoTool is functional.")
        else:
            print(f"\n⚠️  All tests failed. Check API key and network connectivity.")


async def main():
    """Main test function."""
    tester = CladoToolTester()
    success = await tester.run_all_tests()

    if success:
        print("\n✅ At least some CladoTool functions are working!")
        print("Next step: Check agent tool registration and exposure.")
    else:
        print("\n❌ CladoTool tests failed.")
        print("Check your CLADO_API_KEY and network connectivity.")

    return success


def check_environment():
    """Check if the environment is properly configured."""
    print("🔍 Checking environment configuration...")

    # Check for CLADO_API_KEY
    clado_key = os.getenv("CLADO_API_KEY")
    if clado_key:
        print(f"✅ CLADO_API_KEY found: {clado_key[:10]}...")
    else:
        print("❌ CLADO_API_KEY not found in environment")
        print("   Please set CLADO_API_KEY in your .env file")
        return False

    # Check if we can import the required modules
    try:
        from utils.config import config

        print(f"✅ Config module imported successfully")
        print(
            f"   Config CLADO_API_KEY: {'Present' if config.CLADO_API_KEY else 'Missing'}"
        )
    except ImportError as e:
        print(f"❌ Failed to import config: {e}")
        return False

    return True


def run_quick_test():
    """Run a quick synchronous test to check basic functionality."""
    print("\n🚀 Running quick CladoTool import test...")

    try:
        # Test import
        from agent.tools.clado_tool import CladoTool

        print("✅ CladoTool imported successfully")

        # Test initialization
        tool = CladoTool()
        print("✅ CladoTool initialized successfully")
        print(f"   Base URL: {tool.base_url}")
        print(f"   API Key present: {'Yes' if tool.api_key else 'No'}")
        print(f"   Timeout: {tool.timeout}s")
        print(f"   Max retries: {tool.max_retries}")

        # Test schema generation
        schemas = tool.get_schemas()
        print(f"✅ Tool schemas generated: {len(schemas)} methods")
        for method_name in schemas.keys():
            print(f"   - {method_name}")

        return True

    except Exception as e:
        print(f"❌ Quick test failed: {str(e)}")
        return False


if __name__ == "__main__":
    print("🧪 CladoTool Functionality Test Suite")
    print("=" * 50)

    # Check environment first
    if not check_environment():
        print("\n❌ Environment check failed. Please fix configuration issues.")
        sys.exit(1)

    # Run quick test
    if not run_quick_test():
        print("\n❌ Quick test failed. Cannot proceed with full tests.")
        sys.exit(1)

    print("\n" + "=" * 50)
    print("🚀 Starting full async tests...")
    print("=" * 50)

    # Run the full async tests
    result = asyncio.run(main())

    if result:
        print("\n🎉 SUCCESS: CladoTool is working correctly!")
        print("Next steps:")
        print("1. Check agent configuration in database")
        print("2. Verify tool registration in agent runtime")
        print("3. Test agent tool exposure")
    else:
        print("\n❌ FAILURE: CladoTool tests failed.")
        print("Check API key, network connectivity, and error messages above.")

    sys.exit(0 if result else 1)
